"""
Enhanced caching system for image analysis results and processed images.
"""

import hashlib
import json
import logging
import time
from typing import Any, Dict, Optional, Tuple
import numpy as np
from cache_manager import get_cache_manager
from performance_monitor import get_performance_monitor

logger = logging.getLogger(__name__)


class EnhancedImageCache:
    """
    Enhanced caching system for image analysis results with memory optimization.
    """
    
    def __init__(self):
        """Initialize the enhanced cache."""
        self.cache_manager = get_cache_manager()
        self.performance_monitor = get_performance_monitor()
        
        # Memory cache for frequently accessed results
        self.memory_cache: Dict[str, Tuple[Any, float]] = {}
        self.memory_cache_max_size = 100
        self.memory_cache_ttl = 300  # 5 minutes
        
        logger.info("EnhancedImageCache initialized")
    
    def _generate_cache_key(self, url: str, operation: str, **kwargs) -> str:
        """
        Generate a cache key for the given parameters.
        
        Args:
            url: Image URL
            operation: Type of operation (blur_detection, quality_analysis, etc.)
            **kwargs: Additional parameters that affect the result
            
        Returns:
            str: Cache key
        """
        # Create a deterministic key based on URL and parameters
        key_data = {
            "url": url,
            "operation": operation,
            **kwargs
        }
        
        # Sort keys for consistent hashing
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _cleanup_memory_cache(self):
        """Clean up expired entries from memory cache."""
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in self.memory_cache.items()
            if current_time - timestamp > self.memory_cache_ttl
        ]
        
        for key in expired_keys:
            del self.memory_cache[key]
        
        # Limit cache size
        if len(self.memory_cache) > self.memory_cache_max_size:
            # Remove oldest entries
            sorted_items = sorted(
                self.memory_cache.items(),
                key=lambda x: x[1][1]  # Sort by timestamp
            )
            
            # Keep only the most recent entries
            keep_count = self.memory_cache_max_size // 2
            self.memory_cache = dict(sorted_items[-keep_count:])
    
    def get_cached_result(self, url: str, operation: str, **kwargs) -> Optional[Any]:
        """
        Get cached result for the given parameters.
        
        Args:
            url: Image URL
            operation: Type of operation
            **kwargs: Additional parameters
            
        Returns:
            Cached result or None if not found
        """
        cache_key = self._generate_cache_key(url, operation, **kwargs)
        
        # Check memory cache first
        if cache_key in self.memory_cache:
            result, timestamp = self.memory_cache[cache_key]
            if time.time() - timestamp <= self.memory_cache_ttl:
                logger.debug(f"Memory cache hit for {operation} on {url}")
                self.performance_monitor.record_cache_hit(f"{operation}_memory")
                return result
            else:
                # Expired, remove from memory cache
                del self.memory_cache[cache_key]
        
        # Check Redis cache
        try:
            cached_data = self.cache_manager.get(cache_key)
            if cached_data is not None:
                logger.debug(f"Redis cache hit for {operation} on {url}")
                
                # Store in memory cache for faster future access
                self.memory_cache[cache_key] = (cached_data, time.time())
                self._cleanup_memory_cache()
                
                self.performance_monitor.record_cache_hit(f"{operation}_redis")
                return cached_data
        except Exception as e:
            logger.warning(f"Error accessing Redis cache: {e}")
        
        return None
    
    def cache_result(self, url: str, operation: str, result: Any, ttl: int = 3600, **kwargs):
        """
        Cache the result for the given parameters.
        
        Args:
            url: Image URL
            operation: Type of operation
            result: Result to cache
            ttl: Time to live in seconds
            **kwargs: Additional parameters
        """
        cache_key = self._generate_cache_key(url, operation, **kwargs)
        
        try:
            # Store in Redis cache
            self.cache_manager.set(cache_key, result, ttl)
            
            # Store in memory cache for faster access
            self.memory_cache[cache_key] = (result, time.time())
            self._cleanup_memory_cache()
            
            logger.debug(f"Cached result for {operation} on {url}")
            
        except Exception as e:
            logger.warning(f"Error caching result: {e}")
    
    def get_cached_blur_detection(self, url: str, **params) -> Optional[Dict[str, Any]]:
        """Get cached blur detection result."""
        return self.get_cached_result(url, "blur_detection", **params)
    
    def cache_blur_detection(self, url: str, result: Dict[str, Any], **params):
        """Cache blur detection result."""
        self.cache_result(url, "blur_detection", result, ttl=3600, **params)
    
    def get_cached_quality_analysis(self, url: str, **params) -> Optional[Dict[str, Any]]:
        """Get cached quality analysis result."""
        return self.get_cached_result(url, "quality_analysis", **params)
    
    def cache_quality_analysis(self, url: str, result: Dict[str, Any], **params):
        """Cache quality analysis result."""
        self.cache_result(url, "quality_analysis", result, ttl=3600, **params)
    
    def get_cached_comprehensive_analysis(self, url: str, **params) -> Optional[Dict[str, Any]]:
        """Get cached comprehensive analysis result."""
        return self.get_cached_result(url, "comprehensive_analysis", **params)
    
    def cache_comprehensive_analysis(self, url: str, result: Dict[str, Any], **params):
        """Cache comprehensive analysis result."""
        self.cache_result(url, "comprehensive_analysis", result, ttl=3600, **params)
    
    def get_cached_processed_image(self, url: str, processing_params: Dict[str, Any]) -> Optional[np.ndarray]:
        """
        Get cached processed image.
        
        Args:
            url: Image URL
            processing_params: Parameters used for processing (resize settings, etc.)
            
        Returns:
            Cached processed image or None
        """
        # Note: For large images, we might want to cache only the processing metadata
        # and reprocess on demand to save memory
        return self.get_cached_result(url, "processed_image", **processing_params)
    
    def cache_processed_image(self, url: str, image: np.ndarray, processing_params: Dict[str, Any]):
        """
        Cache processed image.
        
        Args:
            url: Image URL
            image: Processed image
            processing_params: Parameters used for processing
        """
        # For memory efficiency, we might want to cache only smaller processed images
        image_size_mb = image.nbytes / (1024 * 1024)
        
        if image_size_mb < 10:  # Only cache images smaller than 10MB
            self.cache_result(url, "processed_image", image, ttl=1800, **processing_params)
        else:
            logger.debug(f"Skipping cache for large processed image ({image_size_mb:.1f}MB)")
    
    def invalidate_cache(self, url: str, operation: Optional[str] = None):
        """
        Invalidate cache entries for a URL.
        
        Args:
            url: Image URL
            operation: Specific operation to invalidate, or None for all
        """
        if operation:
            cache_key = self._generate_cache_key(url, operation)
            try:
                self.cache_manager.delete(cache_key)
                if cache_key in self.memory_cache:
                    del self.memory_cache[cache_key]
                logger.debug(f"Invalidated cache for {operation} on {url}")
            except Exception as e:
                logger.warning(f"Error invalidating cache: {e}")
        else:
            # Invalidate all operations for this URL
            operations = ["blur_detection", "quality_analysis", "comprehensive_analysis", "processed_image"]
            for op in operations:
                self.invalidate_cache(url, op)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "memory_cache_size": len(self.memory_cache),
            "memory_cache_max_size": self.memory_cache_max_size,
            "memory_cache_ttl": self.memory_cache_ttl,
            "redis_cache_enabled": self.cache_manager.enabled if self.cache_manager else False
        }
    
    def clear_memory_cache(self):
        """Clear the memory cache."""
        self.memory_cache.clear()
        logger.info("Memory cache cleared")


# Global enhanced cache instance
_enhanced_cache: Optional[EnhancedImageCache] = None


def get_enhanced_cache() -> EnhancedImageCache:
    """
    Get the global enhanced cache instance.
    
    Returns:
        EnhancedImageCache: Global cache instance
    """
    global _enhanced_cache
    if _enhanced_cache is None:
        _enhanced_cache = EnhancedImageCache()
    return _enhanced_cache
